<?php
/**
 * Close all tickets which have been with the customer for 14 days
 *
 * @package DatabaseAdmin
 * <AUTHOR> <mtu<PERSON><EMAIL>>
 */

use Plusnet\ComparingServiceQuality\Service\ServiceManager as CsqServiceManager;
use Plusnet\ComparingServiceQuality\Service\FaultService as CsqFaultService;

// Includes

require_once('/local/data/mis/database/standard_include.inc');
require_once('../../config/config.inc');
require_once('/local/data/mis/database/database_libraries/tickets-access.inc');
require_once('/local/data/mis/database/database_libraries/userdata-access.inc');
require_once '/local/www/database-admin/phplib/db_mysql.inc';
require_once '/local/www/database-admin/phplib/ct_sql.inc';
require_once '/local/www/database-admin/phplib/session.inc';
require_once '/local/www/database-admin/phplib/auth.inc';
require_once '/local/www/database-admin/phplib/perm.inc';
require_once '/local/www/database-admin/phplib/user.inc';
require_once '/local/www/database-admin/phplib/local.inc';
require_once '/local/www/database-admin/phplib/page.inc';
require_once '/local/www/database-admin/phplib/template.inc';


//
// Main Script
//


set_time_limit(0);

$my_id = SCRIPT_USER;
$whoami = 'Script';
$csqService = CsqServiceManager::getService('FaultService');

//make sure we have $auth object
$auth = new Example_Auth();
$auth->objBusTierSession = new Auth_BusTierSession(SoapSession::getScriptUserDetails('PLUSNET'));


//Get all ticket ids where the ticket has been open with the customer for too long.
$query="SELECT t.ticket_id, t.service_id, ts.chrMobileNumber
        FROM   tickets t
        LEFT JOIN tblTicketSms ts ON t.ticket_id = ts.intTicketId
        WHERE  t.date_raised < DATE_SUB(NOW(), INTERVAL 14 DAY)
        AND    t.status IN ('Open','Assigned','Actioned')
        AND    t.team_id = -1
        ORDER BY t.ticket_id";

$conn = get_named_connection('tickets');
$result = mysql_query($query, $conn) or die(mysql_error());

$body="Question closed automatically after 14 days of inactivity.";
$count = 0;

while ($row = mysql_fetch_array($result, MYSQL_ASSOC)) {

    $ticket_id = checkLastContact($row['ticket_id']);

    if ($ticket_id != 0) {

        $csqService->closeUnresolvedFaultRecords($ticket_id, CsqFaultService::SCRIPT_USER, true);
        Db_Manager::commit(\Db_Manager::DEFAULT_TRANSACTION);

        tickets_ticket_contacts_add($ticket_id, 0, $body, 0, 0, 0, 'Closed', SCRIPT_USER);

        // Check for an open SMS session on this ticket and close it if necessary
        $strMobileNumber = $row['chrMobileNumber'];
        $session = Sms_Session::findSession($strMobileNumber, 'Sms_TicketHandler', $ticket_id, time(), time());

        if (!is_null($session)) {
            $session->expire();
        }

        ++$count;
    }
}

//Tidy up and exit
mysql_free_result($result);

echo("\n$count Questions closed\n");

//
// End Main Script
//




//
// Functions
//

/**
 * Check Last Contact
 *
 * @param integer $ticket_id Ticket Id
 *
 * @return integer Ticket Id
 */
function checkLastContact($ticket_id)
{

    $query = "SELECT count(*) AS count
                FROM ticket_contacts
               WHERE ticket_id = '$ticket_id'
                 AND status IN ('Open','Assigned','Actioned')
                 AND (contact_id IN (SELECT intFlaggedAsComplaintTicketContactId FROM tblComplaintTicketHistory)
                 OR date_raised >= DATE_SUB(NOW(), INTERVAL 14 DAY))";

    $conn = get_named_connection('tickets');

    $result = mysql_query($query, $conn) or die(mysql_error());

    $row = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);

    if ($row['count'] == 0) {

        return $ticket_id;
    } else {

        return 0;
    }
}
