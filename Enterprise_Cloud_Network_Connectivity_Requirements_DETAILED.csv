Source,Destination,Protocol,Port,Direction,Comments
workplace01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
workplace01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MyS<PERSON>
workplace01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
workplace01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
workplace01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
workplace01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MyS<PERSON>
workplace01,master.tr069db.failover.plus.net,TCP,3306,Outbound,TR069 database - MySQL
workplace01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
workplace01,readonly.vmbu.failover.plus.net,TCP,3306,Outbound,VMBU database - MyS<PERSON>
billing01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
billing01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
billing01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
billing01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
billing01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
billing01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
billing01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
cronhost01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
cronhost01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
cronhost01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
cronhost01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
cronhost01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
cronhost01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
cronhost01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
portal,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
portal,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
portal,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
portal,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
portal,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
portal,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
portal,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
pbe,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
pbe,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
pbe,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
pbe,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
pbe,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
tools01,10.255.2.58,TCP,3306,Outbound,Rollout Control database - MySQL
wlrdeds01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
wlrdeds01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
wlrdeds01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
wlrdeds01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
buynet01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
workplace01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
workplace01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
workplace01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
workplace01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
workplace01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
workplace01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
workplace01,api.int.plus.net,HTTPS,443,Outbound,Internal API
billing01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
billing01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
billing01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
billing01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
billing01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
billing01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
billing01,api.int.plus.net,HTTPS,443,Outbound,Internal API
cronhost01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
cronhost01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
cronhost01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
cronhost01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
cronhost01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
cronhost01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
cronhost01,api.int.plus.net,HTTPS,443,Outbound,Internal API
portal,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
portal,billingapi.plus.net,HTTPS,443,Outbound,Billing API
portal,l2capi.plus.net,HTTPS,443,Outbound,L2C API
portal,f2capi.plus.net,HTTPS,443,Outbound,F2C API
portal,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
portal,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
portal,api.int.plus.net,HTTPS,443,Outbound,Internal API
pbe,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
pbe,billingapi.plus.net,HTTPS,443,Outbound,Billing API
pbe,l2capi.plus.net,HTTPS,443,Outbound,L2C API
pbe,f2capi.plus.net,HTTPS,443,Outbound,F2C API
pbe,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
pbe,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
pbe,api.int.plus.net,HTTPS,443,Outbound,Internal API
workplace01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
workplace01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
workplace01,appointing.int.plus.net,HTTP,8280,Outbound,Engineer Appointment Gateway
workplace01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
workplace01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
billing01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
billing01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
billing01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
billing01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
cronhost01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
cronhost01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
cronhost01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
cronhost01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
portal,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
portal,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
portal,appointing.int.plus.net,HTTP,8280,Outbound,Engineer Appointment Gateway
portal,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
portal,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
pbe,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
pbe,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
pbe,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
pbe,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
workplace01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
workplace01,************,HTTP,3128,Outbound,SMS Gateway Proxy
billing01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
billing01,************,HTTP,3128,Outbound,SMS Gateway Proxy
cronhost01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
cronhost01,************,HTTP,3128,Outbound,SMS Gateway Proxy
portal,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
portal,************,HTTP,3128,Outbound,SMS Gateway Proxy
pbe,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
pbe,************,HTTP,3128,Outbound,SMS Gateway Proxy
workplace01,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
billing01,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
portal,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 1 - Workplace Portal HTTP
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 2 - Workplace Portal HTTP
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 3 - Workplace Portal HTTP
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 4 - Workplace Portal HTTP
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 5 - Workplace Portal HTTP
************/32,workplace01,HTTP,80,Inbound,PCI Load Balancer 6 - Workplace Portal HTTP
212.159.2.3/32,workplace01,HTTPS,443,Inbound,SSL Termination Load Balancer - Workplace Portal HTTPS
0.0.0.0/0,workplace01,HTTPS,443,Inbound,Public Internet - Workplace Portal HTTPS (Customer Access)
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 1 - Billing Portal HTTP
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 2 - Billing Portal HTTP
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 3 - Billing Portal HTTP
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 4 - Billing Portal HTTP
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 5 - Billing Portal HTTP
************/32,billing01,HTTP,80,Inbound,PCI Load Balancer 6 - Billing Portal HTTP
212.159.2.3/32,billing01,HTTPS,443,Inbound,SSL Termination Load Balancer - Billing Portal HTTPS
0.0.0.0/0,billing01,HTTPS,443,Inbound,Public Internet - Billing Portal HTTPS (Customer Access)
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 1 - Customer Portal HTTP
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 2 - Customer Portal HTTP
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 3 - Customer Portal HTTP
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 4 - Customer Portal HTTP
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 5 - Customer Portal HTTP
************/32,portal,HTTP,80,Inbound,PCI Load Balancer 6 - Customer Portal HTTP
212.159.2.3/32,portal,HTTPS,443,Inbound,SSL Termination Load Balancer - Customer Portal HTTPS
0.0.0.0/0,portal,HTTPS,443,Inbound,Public Internet - Customer Portal HTTPS (Customer Access)
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 1 - Buynet Portal HTTP
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 2 - Buynet Portal HTTP
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 3 - Buynet Portal HTTP
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 4 - Buynet Portal HTTP
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 5 - Buynet Portal HTTP
************/32,buynet01,HTTP,80,Inbound,PCI Load Balancer 6 - Buynet Portal HTTP
212.159.2.3/32,buynet01,HTTPS,443,Inbound,SSL Termination Load Balancer - Buynet Portal HTTPS
0.0.0.0/0,buynet01,HTTPS,443,Inbound,Public Internet - Buynet Portal HTTPS (Customer Access)
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 1 - DD Verification HTTP
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 2 - DD Verification HTTP
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 3 - DD Verification HTTP
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 4 - DD Verification HTTP
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 5 - DD Verification HTTP
************/32,DD-Verification-Service,HTTP,10803,Inbound,PCI Load Balancer 6 - DD Verification HTTP
212.159.2.3/32,DD-Verification-Service,HTTPS,44303,Inbound,SSL Termination Load Balancer - DD Verification HTTPS
0.0.0.0/0,DD-Verification-Service,HTTPS,44303,Inbound,Public Internet - DD Verification HTTPS (Customer Access)
0.0.0.0/0,DD-Verification-Service,HTTPS,44313,Inbound,Public Internet - DD Verification HTTPS Additional (Customer Access)
DD-Verification-Service,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
DD-Verification-Service,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 1 - Payment Application HTTP
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 2 - Payment Application HTTP
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 3 - Payment Application HTTP
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 4 - Payment Application HTTP
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 5 - Payment Application HTTP
************/32,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,PCI Load Balancer 6 - Payment Application HTTP
212.159.2.3/32,GenericImmediatePaymentApplication-Service,HTTPS,44301,Inbound,SSL Termination Load Balancer - Payment Application HTTPS
0.0.0.0/0,GenericImmediatePaymentApplication-Service,HTTPS,44301,Inbound,Public Internet - Payment Application HTTPS (Customer Access)
GenericImmediatePaymentApplication-Service,signup.businesstier.plus.net,HTTP,8180,Outbound,Encrypting Gateway Service
GenericImmediatePaymentApplication-Service,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
GenericImmediatePaymentApplication-Service,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
GenericImmediatePaymentApplication-Service,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
GenericImmediatePaymentApplication-Service,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
GenericImmediatePaymentApplication-Service,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
GenericImmediatePaymentApplication-Service,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
GenericImmediatePaymentApplication-Service,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
GenericImmediatePaymentApplication-Service,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
GenericImmediatePaymentApplication-Service,billingapi.plus.net,HTTPS,443,Outbound,Billing API
GenericImmediatePaymentApplication-Service,l2capi.plus.net,HTTPS,443,Outbound,L2C API
GenericImmediatePaymentApplication-Service,f2capi.plus.net,HTTPS,443,Outbound,F2C API
GenericImmediatePaymentApplication-Service,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
GenericImmediatePaymentApplication-Service,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
GenericImmediatePaymentApplication-Service,api.int.plus.net,HTTPS,443,Outbound,Internal API
GenericImmediatePaymentApplication-Service,l2c.api.external.plus.net,HTTPS,443,Outbound,External L2C API
GenericImmediatePaymentApplication-Service,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
GenericImmediatePaymentApplication-Service,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
GenericImmediatePaymentApplication-Service,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
GenericImmediatePaymentApplication-Service,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
0.0.0.0/0,pbe,HTTP,8080,Inbound,Public Internet - Acquisition Order API (Partner Integration)
0.0.0.0/0,pbe,HTTP,8080,Inbound,Public Internet - Location API (Partner Integration)
tools01,bastion01,SSH,22,Outbound,Bastion Host Access
tools01,peh-bastion02,SSH,22,Outbound,Bastion Host Access
workplace01,*************,HTTP,50100,Outbound,TV Service Endpoints
workplace01,**************,HTTP,80,Outbound,VOSP Service
pbe,*************,HTTP,50100,Outbound,TV Service Endpoints
pbe,**************,HTTP,80,Outbound,VOSP Service
