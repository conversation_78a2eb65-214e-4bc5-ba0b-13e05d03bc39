Source,Destination,Protocol,Port,Direction,Comments
workplace01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
workplace01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MyS<PERSON>
workplace01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
workplace01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
workplace01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
workplace01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MyS<PERSON>
workplace01,master.tr069db.failover.plus.net,TCP,3306,Outbound,TR069 database - MySQL
workplace01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
workplace01,readonly.vmbu.failover.plus.net,TCP,3306,Outbound,VMBU database - MyS<PERSON>
billing01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
billing01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
billing01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
billing01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
billing01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
billing01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
billing01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
cronhost01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
cronhost01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
cronhost01,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
cronhost01,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
cronhost01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
cronhost01,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
cronhost01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
portal,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
portal,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
portal,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
portal,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
portal,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
portal,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
portal,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
pbe,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
pbe,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
pbe,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
pbe,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
pbe,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
tools01,10.255.2.58,TCP,3306,Outbound,Rollout Control database - MySQL
wlrdeds01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
wlrdeds01,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
wlrdeds01,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
wlrdeds01,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
buynet01,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
workplace01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
workplace01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
workplace01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
workplace01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
workplace01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
workplace01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
workplace01,api.int.plus.net,HTTPS,443,Outbound,Internal API
billing01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
billing01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
billing01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
billing01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
billing01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
billing01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
billing01,api.int.plus.net,HTTPS,443,Outbound,Internal API
cronhost01,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
cronhost01,billingapi.plus.net,HTTPS,443,Outbound,Billing API
cronhost01,l2capi.plus.net,HTTPS,443,Outbound,L2C API
cronhost01,f2capi.plus.net,HTTPS,443,Outbound,F2C API
cronhost01,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
cronhost01,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
cronhost01,api.int.plus.net,HTTPS,443,Outbound,Internal API
portal,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
portal,billingapi.plus.net,HTTPS,443,Outbound,Billing API
portal,l2capi.plus.net,HTTPS,443,Outbound,L2C API
portal,f2capi.plus.net,HTTPS,443,Outbound,F2C API
portal,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
portal,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
portal,api.int.plus.net,HTTPS,443,Outbound,Internal API
pbe,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
pbe,billingapi.plus.net,HTTPS,443,Outbound,Billing API
pbe,l2capi.plus.net,HTTPS,443,Outbound,L2C API
pbe,f2capi.plus.net,HTTPS,443,Outbound,F2C API
pbe,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
pbe,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
pbe,api.int.plus.net,HTTPS,443,Outbound,Internal API
workplace01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
workplace01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
workplace01,appointing.int.plus.net,HTTP,8280,Outbound,Engineer Appointment Gateway
workplace01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
workplace01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
billing01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
billing01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
billing01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
billing01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
cronhost01,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
cronhost01,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
cronhost01,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
cronhost01,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
portal,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
portal,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
portal,appointing.int.plus.net,HTTP,8280,Outbound,Engineer Appointment Gateway
portal,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
portal,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
pbe,wlr3.businesstier.plus.net,HTTP,8280,Outbound,WLR3 Gateway Service
pbe,services.int.plus.net,HTTP,8080,Outbound,Internal Java Services
pbe,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
pbe,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
workplace01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
workplace01,************,HTTP,3128,Outbound,SMS Gateway Proxy
billing01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
billing01,************,HTTP,3128,Outbound,SMS Gateway Proxy
cronhost01,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
cronhost01,************,HTTP,3128,Outbound,SMS Gateway Proxy
portal,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
portal,************,HTTP,3128,Outbound,SMS Gateway Proxy
pbe,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
pbe,************,HTTP,3128,Outbound,SMS Gateway Proxy
workplace01,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
billing01,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
portal,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
External,workplace01,HTTP,80,Inbound,Workplace Portal HTTP
External,workplace01,HTTPS,443,Inbound,Workplace Portal HTTPS
External,billing01,HTTP,80,Inbound,Billing Portal HTTP
External,billing01,HTTPS,443,Inbound,Billing Portal HTTPS
External,portal,HTTP,80,Inbound,Customer Portal HTTP
External,portal,HTTPS,443,Inbound,Customer Portal HTTPS
External,buynet01,HTTP,80,Inbound,Buynet Portal HTTP
External,buynet01,HTTPS,443,Inbound,Buynet Portal HTTPS
External,DD-Verification-Service,HTTP,10803,Inbound,Direct Debit Verification HTTP
External,DD-Verification-Service,HTTPS,44303,Inbound,Direct Debit Verification HTTPS
External,DD-Verification-Service,HTTPS,44313,Inbound,Direct Debit Verification HTTPS Additional
DD-Verification-Service,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
DD-Verification-Service,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
DD-Verification-Service,bitbucket.int.plus.net,HTTPS,443,Outbound,Git repository access
DD-Verification-Service,go.dev,HTTPS,443,Outbound,Go Language Downloads
External,GenericImmediatePaymentApplication-Service,HTTP,10800,Inbound,Payment Application HTTP
External,GenericImmediatePaymentApplication-Service,HTTPS,44301,Inbound,Payment Application HTTPS
GenericImmediatePaymentApplication-Service,signup.businesstier.plus.net,HTTP,8180,Outbound,Encrypting Gateway Service
GenericImmediatePaymentApplication-Service,bitbucket.int.plus.net,HTTPS,443,Outbound,Git repository access
GenericImmediatePaymentApplication-Service,docker-registry.env.plus.net,HTTPS,443,Outbound,Docker registry access
GenericImmediatePaymentApplication-Service,master.coredb.failover.plus.net,TCP,3306,Outbound,Core database - MySQL
GenericImmediatePaymentApplication-Service,master.vcdb.failover.plus.net,TCP,3306,Outbound,VC database - MySQL
GenericImmediatePaymentApplication-Service,master.soulstone.failover.plus.net,TCP,3306,Outbound,Soulstone database - MySQL
GenericImmediatePaymentApplication-Service,master.oadb.failover.plus.net,TCP,3307,Outbound,OA database - MySQL
GenericImmediatePaymentApplication-Service,master.raddb.failover.plus.net,TCP,3306,Outbound,Radius database - MySQL
GenericImmediatePaymentApplication-Service,master.wlrdb.failover.plus.net,TCP,3306,Outbound,WLR database - MySQL
GenericImmediatePaymentApplication-Service,master.maildb.sql.failover.plus.net,TCP,3306,Outbound,Mail database - MySQL
GenericImmediatePaymentApplication-Service,crmservices.plus.net,HTTPS,443,Outbound,CRM Services API
GenericImmediatePaymentApplication-Service,billingapi.plus.net,HTTPS,443,Outbound,Billing API
GenericImmediatePaymentApplication-Service,l2capi.plus.net,HTTPS,443,Outbound,L2C API
GenericImmediatePaymentApplication-Service,f2capi.plus.net,HTTPS,443,Outbound,F2C API
GenericImmediatePaymentApplication-Service,ocsapi.plus.net,HTTPS,443,Outbound,OCS API
GenericImmediatePaymentApplication-Service,c2mapi.plus.net,HTTPS,443,Outbound,C2M API
GenericImmediatePaymentApplication-Service,api.int.plus.net,HTTPS,443,Outbound,Internal API
GenericImmediatePaymentApplication-Service,l2c.api.external.plus.net,HTTPS,443,Outbound,External L2C API
GenericImmediatePaymentApplication-Service,bis.btbuynet.bt.com,HTTPS,50120,Outbound,BT Buynet Payment Gateway
GenericImmediatePaymentApplication-Service,jbpm.businesstier.plus.net,HTTP,8080,Outbound,BPM Service (SOAP)
GenericImmediatePaymentApplication-Service,session.businesstier.plus.net,HTTP,8080,Outbound,Session Manager Service (SOAP)
GenericImmediatePaymentApplication-Service,mail.plus.net,SMTP,25,Outbound,SMTP Email Relay
External,pbe,HTTP,8080,Inbound,Acquisition Order API (Spring Boot)
External,pbe,HTTP,8080,Inbound,Location API (Spring Boot)
pbe,nexus3.env.plus.net,HTTP,80,Outbound,Nexus Repository for JAR downloads
portal,nexus3.env.plus.net,HTTP,80,Outbound,Nexus Repository for Symfony apps
tools01,bastion01,SSH,22,Outbound,Bastion Host Access
tools01,peh-bastion02,SSH,22,Outbound,Bastion Host Access
workplace01,*************,HTTP,50100,Outbound,TV Service Endpoints
workplace01,**************,HTTP,80,Outbound,VOSP Service
pbe,*************,HTTP,50100,Outbound,TV Service Endpoints
pbe,**************,HTTP,80,Outbound,VOSP Service
tools01,portal,SSH,2201,Outbound,PDE Portal Instance
tools01,workplace,SSH,2211,Outbound,PDE Workplace Instance
tools01,pbe,SSH,2221,Outbound,PDE PBE01 Instance
tools01,pbe,SSH,2222,Outbound,PDE PBE02 Instance
tools01,pbe,SSH,2223,Outbound,PDE PBE03 Instance
tools01,wlrdeds,SSH,2261,Outbound,PDE WLR DEDS Instance
tools01,buynet,SSH,2300,Outbound,PDE Buynet Instance
All Containers,coredb,TCP,3307,Outbound,PDE Core Database Instance
All Containers,wlrdb,TCP,3308,Outbound,PDE WLR Database Instance
All Containers,sessiondb,TCP,3310,Outbound,PDE Session Database Instance
All Containers,appdb,TCP,3311,Outbound,PDE App/VC Database Instance
All Containers,cbcdb,TCP,3312,Outbound,PDE CBC Database Instance
All Containers,maildb,TCP,3313,Outbound,PDE Mail Database Instance
All Containers,raddb,TCP,3314,Outbound,PDE Radius Database Instance
All Containers,vmbu,TCP,3315,Outbound,PDE VMBU Database Instance
All Containers,archivedb,TCP,3318,Outbound,PDE Archive Database Instance
All Containers,oadb,TCP,3319,Outbound,PDE OA Database Instance
All Containers,nexus3.env.plus.net,HTTP,80,Outbound,Nexus Repository Access
All Containers,bitbucket.int.plus.net,HTTPS,443,Outbound,Internal Git Repository
