<?php
    require ('/local/data/mis/database/standard_include.inc');
    require ('../../config/config.inc');
    require ('/local/data/mis/database/database_libraries/userdata-access.inc');
    require ('/local/data/mis/database/database_libraries/config-access.inc');
    require ('/local/data/mis/database/database_libraries/tickets-access.inc');
    require ('../../include/misc.inc');

    function disable_account_execute($service_id)
        {
        global $global_component_configurators;

        $service = userdata_service_get($service_id);

        // Store the account's current status
        userdata_keyed_service_note_set($service_id, 'PRE_DISABLE_STATUS', $service['status']);

        // Change the service's status
        userdata_service_set_status($service_id, 'queued-deactivate');

        // Signal all components to self-destruction
        disable_all_components($service_id);

        // Log the cancellation in a contact
        tickets_ticket_add('Script', $service_id, 0, 0, 'Closed', 0, 'Account disabled due to invalid credit card details.');

        // Log the cancellation to the event log
        userdata_log_disable($service_id);

        userdata_service_set_status($service_id, 'deactive');
        }

    $body = sprintf("%-10.10s %-20.20s %-17.17s %-11.11s %-10.10s %s\n",
            'Service ID', 'User Name', 'Previous Status', 'Expiry Date', 'Card Type', 'Product Name');

    echo $body;
    flush();

    $connection = get_named_connection('creditDetails');

    $strQuery1 = "SELECT se.service_id, se.username, se.status, sd.name, cr.card_type, cr.card_expiry_date
                  FROM
                        userdata.services se
                        INNER JOIN userdata.users us ON (us.user_id = se.user_id)
                        INNER JOIN userdata.accounts ac ON (ac.customer_id = us.customer_id)
                        INNER JOIN products.service_definitions sd ON (sd.service_definition_id = se.type)
                        INNER JOIN dbCreditDetails.credit_details cr ON (cr.intAccountId = ac.account_id)
                        INNER JOIN userdata.tblCardType ct ON (ct.vchCardType = cr.card_type)
                 WHERE
                        se.type = 71 AND se.status IN ('active', 'queued-activate', 'queued-reactivate')
                        AND (cr.card_expiry_date < DATE_FORMAT(NOW(),'%Y-%m-%d')
                             OR cr.card_expiry_date = '0000-00-00'
                             OR cr.card_expiry_date = '9999-09-09')
                 ORDER BY cr.card_expiry_date";

    $strQuery2 = "SELECT se.service_id, se.username, se.status, sd.name, cr.card_type, cr.card_expiry_date
                  FROM
                        userdata.services se
                        INNER JOIN userdata.users us ON (us.user_id = se.user_id)
                        INNER JOIN userdata.accounts ac ON (ac.customer_id = us.customer_id)
                        INNER JOIN products.service_definitions sd ON (sd.service_definition_id = se.type)
                        INNER JOIN dbCreditDetails.credit_details cr ON (cr.intAccountId = ac.account_id)
                        INNER JOIN userdata.tblCardType ct ON (ct.vchCardType = cr.card_type)
                 WHERE
                        sd.requires = 'friaco' AND se.status IN ('active', 'queued-activate', 'queued-reactivate')
                        AND (cr.card_expiry_date < DATE_FORMAT(NOW(),'%Y-%m-%d')
                             OR cr.card_expiry_date = '0000-00-00'
                             OR cr.card_expiry_date = '9999-09-09')
                 ORDER BY cr.card_expiry_date";

    $queries = array ($strQuery1, $strQuery2);

    while (list (, $query) = each ($queries))
    {
        $results = mysql_query($query, $connection) or die(mysql_error());

        $details = array();
        while ($row = mysql_fetch_array($results))
        {
            $details[] = $row;
        }
        mysql_free_result($results);

        // Deactivate the accounts
        while (list (, $row) = each ($details))
        {
            $line = sprintf("%-10.10s %-20.20s %-17.17s %-11.11s %-10.10s %s\n",
                    $row['service_id'], $row['username'], $row['status'], $row['card_expiry_date'], $row['card_type'], $row['name']);

            echo $line;
            flush();
            $body .= $line;

            disable_account_execute($row['service_id']);
        }

        unset($details);
    }

    mail('<EMAIL>,<EMAIL>','service_deactivate_bad_cc_details.php',$body);
?>
