<?php

    ////////////////////////////////////////////////////////////////////////////////
    // File:    task_11974.php
    // Purpose: Send emails to 0845 customers recommending the new Connect accounts
    // Date:    25/9/2001
    ////////////////////////////////////////////////////////////////////////////////

    require_once('/local/data/mis/database/standard_include.inc');
    require_once('../../config/config.inc');
    require_once('/local/data/mis/database/database_libraries/userdata-access.inc');
    require_once('/local/data/mis/database/database_libraries/tickets-access.inc');

    ob_end_flush();
    set_time_limit(0);


    $DEBUG = TRUE;
    $error_mails_to = '<EMAIL>';
    $debug_mails_to = '<EMAIL>';

    $connection = get_named_connection('userdata_reporting', FALSE)
                  or error('Failed to connect to database');

    $query = 'SELECT s.service_id AS service_id,
                     s.username AS username,
                     s.type AS account_type,
                     s.isp AS isp,
                     u.forenames AS forenames,
                     u.surname AS surname,
                     u.email AS alternative_email
                FROM services s,
                     components c,
                     users u
               WHERE s.service_id = c.service_id
                 AND u.user_id = s.user_id
                 AND s.type IN ("199","205","211")
                 AND c.component_type_id = "12"
                 AND s.status IN ("active", "queued-activate", "queued-reactivate")
                 AND c.status = "unconfigured"
            ORDER BY service_id ASC';

    $result = mysql_query($query, $connection)
              or error('Failed to query database - '.mysql_error($connection));

    while($data = mysql_fetch_array($result, MYSQL_ASSOC))
    {
        $account_type = $data['account_type'];
        $service_id = intval($data['service_id']);
        $username = $data['username'];
        $isp = $data['isp'];
        $full_name = ucwords(strtolower($data['forenames'])) . ' ' .  ucwords(strtolower($data['surname']));
        $alternative_email = $data['alternative_email'];

        if($DEBUG)
        {
            if(!$sent_mails[$isp])
            {
                $number_of_mails_sent = send_mail($service_id, $account_type, $full_name, $alternative_email, $isp, $username);
                $sent_mails[$isp] = 1;
            }
        }
        else
        {
            $number_of_mails_sent = send_mail($service_id, $account_type, $full_name, $alternative_email, $isp, $username);
            $sent_mails[$isp] += 1;
        }
    }

    echo(dump_structure_to_string($sent_mails));

    echo("\nFINISHED: sent to $number_of_mails_sent\n");



    function send_mail($service_id, $account_type, $full_name, $alternative_email, $isp, $username)
    {
        global $DEBUG, $debug_mails_to;

        static $sent_to_number = 0;
        static $block_mails_sent = 0;

        $mail_subject = 'Important information about your {visp} Connect Lite User account';
        $mail_body = 'Dear {full_name},

* * This e-mail contains important information regarding your {visp} Connect Lite User account - please do read it carefully * *


We\'ve noticed that there has been a lack of demand for the free .co.uk domain offer that we originally made with the Connect Lite User account, and because of this, the option is to be removed.  We believe that the Connect Lite User account, at only �6.99 per month, and giving you up to 20 unmetered hours of Internet access per week, already provides an exceptional level of service.

Of course, because we were making this offer when you signed up for your Connect Lite User Account, you are fully entitled to register your free .co.uk domain name should you wish.  Please read the information presented to you online at our web site {visp_url}/info2/domains.html. This explains in more detail exactly what a domain name is, how you can use it and what you need to bear in mind regarding future renewal, transfer or hosting.

If you wish to take advantage of your free .co.uk domain we ask that you register it within the next 7 days.  Please do note that the offer of the free .co.uk domain will be withdrawn from all Connect Lite User accounts on 10th October 2001.

If you have any questions about this, or any other matter, please do not hesitate to \'Contact Us\' via the Portal, or call our friendly Support Team on {support_tel}.


Regards,
Customer Support

{visp_url}
{strapline}';


        if(trim($full_name) == '')
        {
            $full_name = 'Customer';
        }

        switch($isp)
        {
            case 'plus.net':
                $email_address = 'postmaster@'.$username.'.plus.com';
                $mail_from = '<EMAIL>';
                $visp_url = 'http://www.plus.net';
                $support_number = '0800 432 0200 or 0345 140 0200';
                $visp = 'PlusNet';
                $strapline = 'My Referrals - It pays to recommend PlusNet';
            break;

            case 'force9':
                $email_address = 'postmaster@'.$username.'.force9.co.uk';
                $mail_from = '<EMAIL>';
                $support_number = '0845 1400250';
                $visp = 'Force9';
                $visp_url = 'http://www.force9.net';
                $strapline = 'My Referrals - It\'s payback time at Force9';
            break;

            case 'freeonline':
                $email_address = 'postmaster@'.$username.'.free-online.co.uk';
                $mail_from = '<EMAIL>';
                $support_number = '0845 1400030';
                $visp = 'Free-Online';
                $visp_url = 'http://www.free-online.net';
                $strapline = 'My Referrals - Tell your friends about Free-Online';
            break;

            default:
                error("Unknown isp of $isp in send_mail($service_id, $account_type, $full_name, $alternative_email, $isp)", FALSE);
                return;
            break;
        }

        $mail_header = "From: $mail_from\nReturn-Path: $mail_from\nReply-To: $mail_from\n";
        $mail_subject = str_replace('{visp}', $visp, $mail_subject);
        $mail_body = str_replace('{full_name}', $full_name, $mail_body);
        $mail_body = str_replace('{visp}', $visp, $mail_body);
        $mail_body = str_replace('{support_tel}', $support_number, $mail_body);
        $mail_body = str_replace('{visp_url}', $visp_url, $mail_body);
        $mail_body = str_replace('{strapline}', $strapline, $mail_body);
        $mail_body = str_replace("\r\n", "\n", $mail_body);
        $mail_body = wordwrap($mail_body, "72", "\n");

        if($DEBUG)
        {
            mail($debug_mails_to, $mail_subject, $mail_body, $mail_header);
            ++$sent_to_number;
            $ticket_text = "The customer was informed, by email ($email_address), about the free .co.uk domain on their account.";
            tickets_ticket_add('Script', $service_id, 0, 0, 'Closed', 0, $ticket_text);
            echo("$service_id\n");
        }
        else
        {
            // Send to postmaster email address and add ticket
            $ticket_text = "The customer was informed, by email ($email_address), about the free .co.uk domain on their account.";
            tickets_ticket_add('Script', $service_id, 0, 0, 'Closed', 0, $ticket_text);
            mail($email_address, $mail_subject, $mail_body, $mail_header);
            ++$sent_to_number;
            ++$block_mails_sent;
            echo("$service_id - $email_address\n");

            // Send to alternative email address, if applicable, and add ticket
            if($alternative_email_address != $email_address && preg_match('/^([-_0-9a-zA-Z.]{1,}|"[^@]*")@([-_0-9a-zA-Z]{1,}\.){1,}([a-zA-Z]{2,})$/i', $alternative_email_address))
            {
                $ticket_text = "The customer was informed, by email ($alternative_email_address), about the free .co.uk domain on their account.";
                tickets_ticket_add('Script', $service_id, 0, 0, 'Closed', 0, $ticket_text);
                mail($alternative_email_address, $mail_subject, $mail_body, $mail_header);
                ++$sent_to_number;
                ++$block_mails_sent;
                echo("           $alternative_email_address\n");
            }

            echo("\n");
            flush();

            // Wait 3 mins between blocks of 5000 to allow mail server to cope with load
            if($block_mails_sent >= 5000)
            {
                echo("\nDelay between blocks of 5000 mails\n");
                flush();
                sleep(180);
                $block_mails_sent = 0;
            }
        }

        return $sent_to_number;
    }


    /////////////////////////////////////////////////////////////////////////////////////////
    // Function:  get_isp
    // Purpose:   Tries to separate plus.net.uk products from the Force9 namespace
    // Arguments: account_type_id (service_definition_id from products.service_definitions)
    //            account_type_name (name from products.service_definitions)
    // Returns:   string - either 'force9' or 'plus.net.uk'
    /////////////////////////////////////////////////////////////////////////////////////////

    function get_isp($account_type_id, $account_type_name)
    {
        if(stristr($account_type_name, 'plus.net.uk'))
        {
            return 'plus.net.uk';
        }

        switch($account_type_id)
        {
            case '7':        // Netstart
            case '32':       // NetPro64
            case '33':       // NetStart Plus
            case '34':       // NetStart 0800
            case '35':       // NetPro
            case '36':       // NetPilot
            case '37':       // NetPilot 0820
            case '38':       // NetDigital
            case '39':       // NetGateway
            case '40':       // NetComplete
            case '41':       // NetStart Mobile
            case '49':       // NetStart with .co.uk
            case '50':       // NetStart with .com
            case '66':       // Leased Line
            case '79':       // Prospect
            case '142':      // plus.net.uk Business Free
                return 'plus.net.uk';
            break;

            default:
                return 'force9';
            break;
        }
    }


    /////////////////////////////////////////////////////////////////////////////////////////
    // Function:  error
    // Purpose:   Custom error message handler
    // Arguments: message
    //            die - optional
    /////////////////////////////////////////////////////////////////////////////////////////

    function error($message, $die = TRUE)
    {
        global $error_mails_to, $argv;

        echo("\nERROR: $message\n");

        $mail_header = "From: <EMAIL>\nReply-To: <EMAIL>\nReturn-Path: <EMAIL>";
        mail($error_mails_to, "Error in script ".$argv[0], $message, $mail_header);

        if($die)
        {
            die();
        }
    }

?>
